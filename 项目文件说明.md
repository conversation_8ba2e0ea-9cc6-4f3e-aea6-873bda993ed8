# GitHub 文件加速下载器 - 项目文件说明

## 文件结构

```
github-downloader/
├── github_downloader.py      # 核心下载模块（命令行版本）
├── github_downloader_gui.py  # GUI 图形界面版本
├── run_gui.py               # GUI 启动脚本
├── 启动下载器.bat            # Windows 批处理启动文件
├── example.py               # 使用示例和交互式版本
├── requirements.txt         # Python 依赖包列表
├── README.md               # 项目说明文档
├── GUI使用说明.md           # GUI 详细使用说明
├── 项目文件说明.md          # 本文件，项目结构说明
└── test_license.txt        # 测试下载的示例文件
```

## 文件详细说明

### 核心程序文件

#### `github_downloader.py`
- **功能**: 核心下载引擎，提供命令行接口
- **特点**: 
  - 支持多种 GitHub URL 格式解析
  - 镜像站加速下载
  - 自动重试机制
  - 进度显示
- **使用**: `python github_downloader.py <URL>`

#### `github_downloader_gui.py`
- **功能**: 图形用户界面版本
- **特点**:
  - 基于 tkinter 的现代化界面
  - 实时进度窗口
  - 多镜像站选择
  - 用户友好的操作体验
- **使用**: `python github_downloader_gui.py`

### 启动文件

#### `run_gui.py`
- **功能**: GUI 程序的 Python 启动脚本
- **特点**: 包含错误处理和依赖检查
- **使用**: `python run_gui.py`

#### `启动下载器.bat`
- **功能**: Windows 批处理启动文件
- **特点**: 
  - 自动检查 Python 环境
  - 自动安装依赖包
  - 中文界面友好
- **使用**: 双击运行

### 示例和文档

#### `example.py`
- **功能**: 使用示例和交互式命令行版本
- **包含**:
  - 基本使用示例
  - 批量下载示例
  - 交互式下载模式
- **使用**: `python example.py` 或 `python example.py --interactive`

#### `requirements.txt`
- **功能**: Python 依赖包列表
- **内容**: 
  - `requests>=2.25.0` - HTTP 请求库
  - `pathlib2>=2.3.0` - 路径处理（Python < 3.4）

#### `README.md`
- **功能**: 项目主要说明文档
- **内容**:
  - 功能特性介绍
  - 安装和使用方法
  - 命令行参数说明
  - 示例代码

#### `GUI使用说明.md`
- **功能**: GUI 版本详细使用说明
- **内容**:
  - 界面布局说明
  - 操作步骤指南
  - 功能特点介绍
  - 常见问题解答

## 使用建议

### 新手用户
1. **推荐使用 GUI 版本**: 双击 `启动下载器.bat`（Windows）或运行 `python github_downloader_gui.py`
2. **阅读文档**: 先看 `GUI使用说明.md`
3. **测试下载**: 使用简单的文件链接测试功能

### 高级用户
1. **命令行版本**: 使用 `github_downloader.py` 进行批量操作
2. **编程集成**: 导入 `GitHubDownloader` 类到自己的项目
3. **自定义扩展**: 基于核心模块开发自定义功能

### 开发者
1. **核心模块**: `github_downloader.py` 包含所有核心功能
2. **GUI 模块**: `github_downloader_gui.py` 展示了 tkinter 界面开发
3. **扩展示例**: `example.py` 提供了各种使用场景的示例

## 依赖关系

```
github_downloader.py (核心模块)
    ↑
    ├── github_downloader_gui.py (GUI 版本)
    ├── example.py (示例程序)
    └── run_gui.py (启动脚本)
```

- **核心模块独立**: `github_downloader.py` 可以独立使用
- **GUI 依赖核心**: GUI 版本导入并使用核心模块
- **启动脚本**: 提供便捷的启动方式

## 系统要求

### 最低要求
- **Python**: 3.6 或更高版本
- **操作系统**: Windows 7+, macOS 10.12+, Linux (任何现代发行版)
- **内存**: 至少 100MB 可用内存
- **网络**: 互联网连接

### 推荐配置
- **Python**: 3.8 或更高版本
- **内存**: 512MB 或更多可用内存
- **网络**: 稳定的宽带连接

## 许可证信息

- **项目许可**: MIT License
- **依赖包许可**: 
  - `requests`: Apache 2.0 License
  - `tkinter`: Python Software Foundation License
- **镜像站**: 各镜像站有自己的使用条款

## 更新历史

- **v1.0.0**: 初始版本
  - 基本的命令行下载功能
  - 镜像站支持
  - 自动重试机制

- **v1.1.0**: GUI 版本
  - 添加图形用户界面
  - 实时进度显示
  - 用户友好的操作体验
  - Windows 批处理启动文件

## 技术特点

### 网络处理
- **会话管理**: 使用 `requests.Session` 保持连接
- **流式下载**: 大文件分块下载，节省内存
- **错误重试**: 自动重试机制，提高成功率

### 界面设计
- **响应式布局**: 窗口大小可调整
- **进度反馈**: 实时显示下载进度和速度
- **错误处理**: 友好的错误提示和建议

### 跨平台支持
- **路径处理**: 使用 `pathlib` 处理跨平台路径
- **编码支持**: UTF-8 编码，支持中文文件名
- **系统集成**: 与操作系统文件管理器集成
