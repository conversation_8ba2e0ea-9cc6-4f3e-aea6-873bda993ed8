欧# GitHub 文件加速下载器

一个使用 `gh.imixc.top` 镜像站加速下载 GitHub 文件的 Python 程序，提供命令行和图形界面两种使用方式。

## 功能特性

- ✅ 支持下载单个文件
- ✅ 支持下载整个仓库（ZIP 格式）
- ✅ 自动使用镜像站加速下载
- ✅ 支持多种 GitHub URL 格式
- ✅ 实时显示下载进度和速度
- ✅ 自动重试机制（镜像站失败时回退到原站）
- ✅ 命令行和编程接口双重支持
- ✅ **图形用户界面（GUI）支持**
- ✅ 支持自定义镜像站地址
- ✅ 一键粘贴 URL 功能
- ✅ 可视化下载进度窗口

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests
```

## 使用方法

### 1. 图形界面使用（推荐）

#### 快速启动
```bash
# 方法1: 直接运行 Python 脚本
python github_downloader_gui.py

# 方法2: 使用启动脚本
python run_gui.py

# 方法3: Windows 用户双击批处理文件
启动下载器.bat
```

#### GUI 功能说明
- **URL 输入**: 支持粘贴功能，自动识别 GitHub 链接
- **下载类型**: 可选择下载单个文件或整个仓库
- **输出设置**: 可自定义保存路径和文件名
- **镜像站选择**: 支持多个镜像站，可选择不使用镜像
- **实时进度**: 独立的进度窗口显示下载状态和日志
- **错误处理**: 友好的错误提示和自动重试

### 2. 命令行使用

#### 下载单个文件
```bash
# 基本用法
python github_downloader.py https://github.com/microsoft/vscode/blob/main/README.md

# 指定输出文件名
python github_downloader.py https://github.com/microsoft/vscode/blob/main/README.md -o vscode_readme.md

# 不使用镜像站
python github_downloader.py https://github.com/microsoft/vscode/blob/main/README.md --no-mirror
```

#### 下载整个仓库
```bash
# 下载仓库 ZIP 文件
python github_downloader.py https://github.com/python/cpython --repo

# 指定输出目录
python github_downloader.py https://github.com/python/cpython --repo -d downloads
```

#### 使用自定义镜像站
```bash
python github_downloader.py https://github.com/torvalds/linux/blob/master/README --mirror-url https://github.com.cnpmjs.org
```

### 3. 编程接口使用

```python
from github_downloader import GitHubDownloader

# 创建下载器
downloader = GitHubDownloader()

# 下载单个文件
success = downloader.download_file(
    "https://github.com/microsoft/vscode/blob/main/README.md",
    "vscode_readme.md"
)

# 下载仓库
success = downloader.download_repository(
    "https://github.com/python/cpython",
    "downloads"
)

# 使用自定义镜像站
custom_downloader = GitHubDownloader(mirror_url="https://github.com.cnpmjs.org")
```

### 4. 交互式使用

```bash
python example.py --interactive
```

## 支持的 URL 格式

程序支持以下 GitHub URL 格式：

- `https://github.com/owner/repo/blob/branch/path/to/file`
- `https://github.com/owner/repo/raw/branch/path/to/file`
- `https://github.com/owner/repo/tree/branch/path/to/dir`
- `https://github.com/owner/repo/archive/refs/heads/branch.zip`
- `https://github.com/owner/repo` (仓库根目录)

## 命令行参数

```
positional arguments:
  url                   GitHub 文件或仓库 URL

optional arguments:
  -h, --help            显示帮助信息
  -o OUTPUT, --output OUTPUT
                        输出路径
  -d DIR, --dir DIR     输出目录（用于仓库下载）
  --no-mirror           不使用镜像站
  --repo                下载整个仓库
  --mirror-url MIRROR_URL
                        镜像站地址（默认: https://gh.imixc.top）
```

## 示例

### 下载 Python 官方仓库的 README
```bash
python github_downloader.py https://github.com/python/cpython/blob/main/README.rst
```

### 下载 Linux 内核仓库
```bash
python github_downloader.py https://github.com/torvalds/linux --repo -d kernel
```

### 批量下载示例
```python
from github_downloader import GitHubDownloader

downloader = GitHubDownloader()

urls = [
    "https://github.com/python/cpython/blob/main/LICENSE",
    "https://github.com/python/cpython/blob/main/README.rst",
    "https://github.com/python/cpython/blob/main/pyproject.toml"
]

for i, url in enumerate(urls, 1):
    filename = f"python_file_{i}.txt"
    success = downloader.download_file(url, filename)
    print(f"文件 {i}: {'成功' if success else '失败'}")
```

## 错误处理

程序具有完善的错误处理机制：

1. **网络错误**: 自动重试，镜像站失败时回退到原始 GitHub
2. **URL 解析错误**: 提供清晰的错误信息
3. **文件写入错误**: 自动创建目录，处理权限问题
4. **中断处理**: 支持 Ctrl+C 中断下载

## 性能特性

- **分块下载**: 使用流式下载，内存占用低
- **进度显示**: 实时显示下载进度和速度
- **断点续传**: 支持重新开始下载（未实现，可扩展）
- **并发下载**: 可扩展支持多文件并发下载

## 许可证

本项目使用 MIT 许可证。所有依赖项均为开源且可免费商用。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 更新日志

- v1.0.0: 初始版本，支持基本的文件和仓库下载功能
