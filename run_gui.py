#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub 下载器 GUI 启动脚本
"""

import sys
import os

# 添加当前目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

try:
    from github_downloader_gui import main
    
    if __name__ == "__main__":
        print("正在启动 GitHub 文件加速下载器...")
        main()
        
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有依赖已安装:")
    print("pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"启动失败: {e}")
    sys.exit(1)
