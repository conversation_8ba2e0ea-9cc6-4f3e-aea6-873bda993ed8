#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub 下载器使用示例
"""

from github_downloader import GitHubDownloader


def example_usage():
    """使用示例"""
    
    # 创建下载器实例
    downloader = GitHubDownloader()
    
    print("=== GitHub 文件加速下载器示例 ===\n")
    
    # 示例1: 下载单个文件
    print("1. 下载单个文件:")
    file_url = "https://github.com/microsoft/vscode/blob/main/README.md"
    success = downloader.download_file(file_url, "vscode_readme.md")
    print(f"下载结果: {'成功' if success else '失败'}\n")
    
    # 示例2: 下载仓库的 ZIP 文件
    print("2. 下载仓库 ZIP:")
    repo_url = "https://github.com/python/cpython"
    success = downloader.download_repository(repo_url, "downloads")
    print(f"下载结果: {'成功' if success else '失败'}\n")
    
    # 示例3: 使用自定义镜像站
    print("3. 使用自定义镜像站:")
    custom_downloader = GitHubDownloader(mirror_url="https://github.com.cnpmjs.org")
    file_url = "https://github.com/torvalds/linux/blob/master/README"
    success = custom_downloader.download_file(file_url, "linux_readme.txt")
    print(f"下载结果: {'成功' if success else '失败'}\n")
    
    # 示例4: 批量下载文件
    print("4. 批量下载文件:")
    urls = [
        "https://github.com/python/cpython/blob/main/LICENSE",
        "https://github.com/python/cpython/blob/main/README.rst",
        "https://github.com/python/cpython/blob/main/pyproject.toml"
    ]
    
    for i, url in enumerate(urls, 1):
        filename = f"python_file_{i}.txt"
        success = downloader.download_file(url, filename)
        print(f"文件 {i}: {'成功' if success else '失败'}")


def interactive_download():
    """交互式下载"""
    downloader = GitHubDownloader()
    
    print("=== 交互式 GitHub 文件下载 ===")
    print("输入 'quit' 退出程序\n")
    
    while True:
        try:
            url = input("请输入 GitHub 文件或仓库 URL: ").strip()
            
            if url.lower() in ['quit', 'exit', 'q']:
                print("再见！")
                break
            
            if not url:
                continue
            
            # 询问下载类型
            download_type = input("下载类型 (1=文件, 2=仓库ZIP): ").strip()
            
            if download_type == "2":
                output_dir = input("输出目录 (回车使用当前目录): ").strip() or None
                success = downloader.download_repository(url, output_dir)
            else:
                output_path = input("输出文件名 (回车自动命名): ").strip() or None
                success = downloader.download_file(url, output_path)
            
            print(f"下载{'成功' if success else '失败'}！\n")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}\n")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_download()
    else:
        example_usage()
