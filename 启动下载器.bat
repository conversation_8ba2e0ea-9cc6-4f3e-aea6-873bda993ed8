@echo off
chcp 65001 >nul
title GitHub 文件加速下载器

echo ========================================
echo    GitHub 文件加速下载器
echo ========================================
echo.

echo 正在检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 Python，请先安装 Python 3.6 或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python 环境检查通过
echo.

echo 正在检查依赖包...
python -c "import requests, tkinter" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 依赖包安装失败，请手动运行: pip install requests
        pause
        exit /b 1
    )
)

echo 依赖包检查通过
echo.

echo 正在启动 GUI 界面...
python run_gui.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查错误信息
    pause
)
