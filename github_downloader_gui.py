#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub 文件加速下载器 - GUI 版本
使用 tkinter 创建图形用户界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from pathlib import Path
import queue
import time
from github_downloader import GitHubDownloader


class DownloadProgressWindow:
    """下载进度窗口"""
    
    def __init__(self, parent):
        self.window = tk.Toplevel(parent)
        self.window.title("下载进度")
        self.window.geometry("500x300")
        self.window.resizable(True, True)
        
        # 设置窗口居中
        self.window.transient(parent)
        self.window.grab_set()
        
        self.setup_ui()
        self.is_cancelled = False
        
    def setup_ui(self):
        """设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # URL 显示
        ttk.Label(main_frame, text="下载链接:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.url_label = ttk.Label(main_frame, text="", foreground="blue")
        self.url_label.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 进度条
        ttk.Label(main_frame, text="下载进度:").grid(row=2, column=0, sticky=tk.W)
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(5, 5))
        
        # 进度信息
        self.progress_info = ttk.Label(main_frame, text="准备下载...")
        self.progress_info.grid(row=4, column=0, sticky=tk.W, pady=(0, 10))
        
        # 日志区域
        ttk.Label(main_frame, text="下载日志:").grid(row=5, column=0, sticky=tk.W)
        self.log_text = scrolledtext.ScrolledText(main_frame, height=8, width=60)
        self.log_text.grid(row=6, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 10))
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=7, column=0, sticky=(tk.W, tk.E))
        button_frame.columnconfigure(0, weight=1)
        
        # 取消按钮
        self.cancel_button = ttk.Button(button_frame, text="取消下载", command=self.cancel_download)
        self.cancel_button.grid(row=0, column=0, padx=(0, 10))
        
        # 关闭按钮（初始隐藏）
        self.close_button = ttk.Button(button_frame, text="关闭", command=self.close_window)
        
    def update_url(self, url):
        """更新显示的 URL"""
        # 截断过长的 URL
        display_url = url if len(url) <= 70 else url[:67] + "..."
        self.url_label.config(text=display_url)
        
    def update_progress(self, progress, info_text):
        """更新进度"""
        self.progress_var.set(progress)
        self.progress_info.config(text=info_text)
        
    def add_log(self, message):
        """添加日志"""
        self.log_text.insert(tk.END, f"{time.strftime('%H:%M:%S')} - {message}\n")
        self.log_text.see(tk.END)
        
    def download_completed(self, success):
        """下载完成"""
        self.cancel_button.grid_remove()
        self.close_button.grid(row=0, column=0)
        
        if success:
            self.progress_var.set(100)
            self.progress_info.config(text="下载完成！")
            self.add_log("✅ 下载成功完成")
        else:
            self.progress_info.config(text="下载失败")
            self.add_log("❌ 下载失败")
            
    def cancel_download(self):
        """取消下载"""
        self.is_cancelled = True
        self.add_log("⚠️ 用户取消下载")
        self.cancel_button.config(state="disabled", text="正在取消...")
        
    def close_window(self):
        """关闭窗口"""
        self.window.destroy()


class GitHubDownloaderGUI:
    """GitHub 下载器 GUI 主窗口"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("GitHub 文件加速下载器")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # 设置窗口图标（如果有的话）
        try:
            # 可以在这里设置窗口图标
            pass
        except:
            pass
            
        self.downloader = GitHubDownloader()
        self.setup_ui()
        self.setup_styles()
        
    def setup_styles(self):
        """设置样式"""
        style = ttk.Style()
        
        # 配置按钮样式
        style.configure("Accent.TButton", foreground="white")
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="GitHub 文件加速下载器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # URL 输入区域
        url_frame = ttk.LabelFrame(main_frame, text="下载设置", padding="10")
        url_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        url_frame.columnconfigure(1, weight=1)
        
        # GitHub URL
        ttk.Label(url_frame, text="GitHub URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.url_var = tk.StringVar()
        self.url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=50)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 粘贴按钮
        paste_button = ttk.Button(url_frame, text="粘贴", command=self.paste_url)
        paste_button.grid(row=0, column=2)
        
        # 下载类型
        ttk.Label(url_frame, text="下载类型:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.download_type = tk.StringVar(value="file")
        type_frame = ttk.Frame(url_frame)
        type_frame.grid(row=1, column=1, sticky=tk.W, pady=(10, 0))
        
        ttk.Radiobutton(type_frame, text="单个文件", variable=self.download_type, 
                       value="file").grid(row=0, column=0, padx=(0, 20))
        ttk.Radiobutton(type_frame, text="整个仓库(ZIP)", variable=self.download_type, 
                       value="repo").grid(row=0, column=1)
        
        # 输出设置
        output_frame = ttk.LabelFrame(main_frame, text="输出设置", padding="10")
        output_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        output_frame.columnconfigure(1, weight=1)
        
        # 输出路径
        ttk.Label(output_frame, text="保存到:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.output_var = tk.StringVar(value=os.getcwd())
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_var, width=50)
        self.output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        browse_button = ttk.Button(output_frame, text="浏览", command=self.browse_output)
        browse_button.grid(row=0, column=2)
        
        # 文件名（仅文件下载时显示）
        ttk.Label(output_frame, text="文件名:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(10, 0))
        self.filename_var = tk.StringVar()
        self.filename_entry = ttk.Entry(output_frame, textvariable=self.filename_var, width=50)
        self.filename_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 10), pady=(10, 0))
        
        # 高级设置
        advanced_frame = ttk.LabelFrame(main_frame, text="高级设置", padding="10")
        advanced_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # 镜像站设置
        ttk.Label(advanced_frame, text="镜像站:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.mirror_var = tk.StringVar(value="https://gh.imixc.top")
        mirror_combo = ttk.Combobox(advanced_frame, textvariable=self.mirror_var, width=30)
        mirror_combo['values'] = [
            "https://gh.imixc.top",
            "https://github.com.cnpmjs.org",
            "https://hub.fastgit.xyz",
            "不使用镜像站"
        ]
        mirror_combo.grid(row=0, column=1, sticky=tk.W)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=(20, 0))
        
        # 下载按钮
        self.download_button = ttk.Button(button_frame, text="开始下载", 
                                         command=self.start_download, style="Accent.TButton")
        self.download_button.grid(row=0, column=0, padx=(0, 10))
        
        # 清空按钮
        clear_button = ttk.Button(button_frame, text="清空", command=self.clear_form)
        clear_button.grid(row=0, column=1, padx=(0, 10))
        
        # 退出按钮
        exit_button = ttk.Button(button_frame, text="退出", command=self.root.quit)
        exit_button.grid(row=0, column=2)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(20, 0))
        
        # 绑定事件
        self.download_type.trace('w', self.on_download_type_change)
        
    def paste_url(self):
        """粘贴剪贴板内容到 URL 输入框"""
        try:
            clipboard_content = self.root.clipboard_get()
            self.url_var.set(clipboard_content.strip())
        except tk.TclError:
            messagebox.showwarning("警告", "剪贴板为空或无法访问")
            
    def browse_output(self):
        """浏览输出目录"""
        if self.download_type.get() == "file":
            # 选择文件保存位置
            filename = filedialog.asksaveasfilename(
                title="选择保存位置",
                defaultextension=".txt",
                filetypes=[("所有文件", "*.*")]
            )
            if filename:
                directory = os.path.dirname(filename)
                basename = os.path.basename(filename)
                self.output_var.set(directory)
                self.filename_var.set(basename)
        else:
            # 选择目录
            directory = filedialog.askdirectory(title="选择保存目录")
            if directory:
                self.output_var.set(directory)
                
    def on_download_type_change(self, *args):
        """下载类型改变时的处理"""
        if self.download_type.get() == "repo":
            self.filename_entry.config(state="disabled")
        else:
            self.filename_entry.config(state="normal")
            
    def clear_form(self):
        """清空表单"""
        self.url_var.set("")
        self.filename_var.set("")
        self.download_type.set("file")
        self.status_var.set("表单已清空")
        
    def validate_input(self):
        """验证输入"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请输入 GitHub URL")
            return False
            
        if "github.com" not in url:
            messagebox.showerror("错误", "请输入有效的 GitHub URL")
            return False
            
        output_dir = self.output_var.get().strip()
        if not output_dir:
            messagebox.showerror("错误", "请选择输出目录")
            return False
            
        if not os.path.exists(output_dir):
            try:
                os.makedirs(output_dir, exist_ok=True)
            except Exception as e:
                messagebox.showerror("错误", f"无法创建输出目录: {e}")
                return False
                
        return True
        
    def start_download(self):
        """开始下载"""
        if not self.validate_input():
            return
            
        # 禁用下载按钮
        self.download_button.config(state="disabled", text="下载中...")
        self.status_var.set("正在下载...")
        
        # 创建进度窗口
        progress_window = DownloadProgressWindow(self.root)
        progress_window.update_url(self.url_var.get())
        
        # 在新线程中执行下载
        download_thread = threading.Thread(
            target=self.download_worker,
            args=(progress_window,),
            daemon=True
        )
        download_thread.start()
        
    def download_worker(self, progress_window):
        """下载工作线程"""
        try:
            url = self.url_var.get().strip()
            output_dir = self.output_var.get().strip()
            
            # 设置镜像站
            mirror_url = self.mirror_var.get()
            use_mirror = mirror_url != "不使用镜像站"
            
            if use_mirror:
                self.downloader = GitHubDownloader(mirror_url=mirror_url)
            else:
                self.downloader = GitHubDownloader()
            
            progress_window.add_log(f"开始下载: {url}")
            
            if self.download_type.get() == "repo":
                # 下载仓库
                success = self.downloader.download_repository(url, output_dir)
            else:
                # 下载文件
                filename = self.filename_var.get().strip()
                if filename:
                    output_path = os.path.join(output_dir, filename)
                else:
                    output_path = None
                    
                success = self.downloader.download_file(url, output_path, use_mirror=use_mirror)
            
            # 更新 UI
            self.root.after(0, lambda: self.download_completed(success, progress_window))
            
        except Exception as e:
            error_msg = f"下载过程中发生错误: {str(e)}"
            progress_window.add_log(error_msg)
            self.root.after(0, lambda: self.download_completed(False, progress_window))
            
    def download_completed(self, success, progress_window):
        """下载完成后的处理"""
        # 恢复下载按钮
        self.download_button.config(state="normal", text="开始下载")
        
        if success:
            self.status_var.set("下载完成")
            messagebox.showinfo("成功", "文件下载完成！")
        else:
            self.status_var.set("下载失败")
            messagebox.showerror("失败", "文件下载失败，请检查网络连接和 URL")
            
        # 更新进度窗口
        progress_window.download_completed(success)
        
    def run(self):
        """运行 GUI"""
        # 设置窗口居中
        self.center_window()
        self.root.mainloop()
        
    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")


def main():
    """主函数"""
    try:
        app = GitHubDownloaderGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
