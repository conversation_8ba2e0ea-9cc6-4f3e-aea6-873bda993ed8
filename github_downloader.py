#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub 文件加速下载器
使用 gh.imixc.top 镜像站加速下载 GitHub 文件
"""

import os
import sys
import re
import requests
import argparse
from urllib.parse import urlparse, urljoin
from pathlib import Path
import time
from typing import Optional, Tuple


class GitHubDownloader:
    """GitHub 文件下载器，支持镜像站加速"""
    
    def __init__(self, mirror_url: str = "https://gh.imixc.top"):
        """
        初始化下载器
        
        Args:
            mirror_url: 镜像站地址，默认使用 gh.imixc.top
        """
        self.mirror_url = mirror_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def parse_github_url(self, url: str) -> Optional[Tuple[str, str, str, str]]:
        """
        解析 GitHub URL，提取仓库信息
        
        Args:
            url: GitHub 文件或仓库 URL
            
        Returns:
            (owner, repo, branch, path) 或 None
        """
        # 支持多种 GitHub URL 格式
        patterns = [
            # https://github.com/owner/repo/blob/branch/path/to/file
            r'github\.com/([^/]+)/([^/]+)/blob/([^/]+)/(.+)',
            # https://github.com/owner/repo/raw/branch/path/to/file
            r'github\.com/([^/]+)/([^/]+)/raw/([^/]+)/(.+)',
            # https://github.com/owner/repo/tree/branch/path/to/dir
            r'github\.com/([^/]+)/([^/]+)/tree/([^/]+)/(.+)',
            # https://github.com/owner/repo/archive/refs/heads/branch.zip
            r'github\.com/([^/]+)/([^/]+)/archive/refs/heads/([^.]+)\.zip',
            # https://github.com/owner/repo (默认主分支)
            r'github\.com/([^/]+)/([^/]+)/?$'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                groups = match.groups()
                if len(groups) == 2:  # 仓库根目录
                    return groups[0], groups[1], 'main', ''
                elif len(groups) == 3:  # archive zip
                    return groups[0], groups[1], groups[2], f'archive/refs/heads/{groups[2]}.zip'
                else:  # 文件或目录
                    return groups[0], groups[1], groups[2], groups[3]
        
        return None
    
    def build_mirror_url(self, github_url: str) -> str:
        """
        构建镜像站 URL
        
        Args:
            github_url: 原始 GitHub URL
            
        Returns:
            镜像站 URL
        """
        # 直接替换域名
        mirror_url = github_url.replace('github.com', self.mirror_url.replace('https://', ''))
        return mirror_url
    
    def download_file(self, url: str, output_path: Optional[str] = None, 
                     use_mirror: bool = True, chunk_size: int = 8192) -> bool:
        """
        下载单个文件
        
        Args:
            url: 文件 URL
            output_path: 输出路径，如果为 None 则自动生成
            use_mirror: 是否使用镜像站
            chunk_size: 下载块大小
            
        Returns:
            下载是否成功
        """
        try:
            # 构建下载 URL
            download_url = self.build_mirror_url(url) if use_mirror else url
            
            # 如果是 blob 链接，转换为 raw 链接
            if '/blob/' in download_url:
                download_url = download_url.replace('/blob/', '/raw/')
            
            print(f"正在下载: {download_url}")
            
            # 发送请求
            response = self.session.get(download_url, stream=True)
            response.raise_for_status()
            
            # 确定输出文件名
            if output_path is None:
                # 从 URL 或 Content-Disposition 头获取文件名
                filename = self._get_filename_from_response(response, download_url)
                output_path = filename
            
            # 创建输出目录
            output_dir = os.path.dirname(output_path)
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
            
            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            
            # 下载文件
            downloaded_size = 0
            start_time = time.time()
            
            with open(output_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        # 显示进度
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            elapsed_time = time.time() - start_time
                            if elapsed_time > 0:
                                speed = downloaded_size / elapsed_time / 1024 / 1024  # MB/s
                                print(f"\r进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes) "
                                      f"速度: {speed:.2f} MB/s", end='', flush=True)
            
            print(f"\n下载完成: {output_path}")
            return True
            
        except requests.RequestException as e:
            print(f"下载失败: {e}")
            if use_mirror:
                print("尝试使用原始 GitHub 链接...")
                return self.download_file(url, output_path, use_mirror=False, chunk_size=chunk_size)
            return False
        except Exception as e:
            print(f"下载过程中发生错误: {e}")
            return False
    
    def _get_filename_from_response(self, response: requests.Response, url: str) -> str:
        """从响应或 URL 中获取文件名"""
        # 尝试从 Content-Disposition 头获取
        content_disposition = response.headers.get('content-disposition')
        if content_disposition:
            filename_match = re.search(r'filename[*]?=([^;]+)', content_disposition)
            if filename_match:
                filename = filename_match.group(1).strip('"\'')
                return filename
        
        # 从 URL 中提取文件名
        parsed_url = urlparse(url)
        filename = os.path.basename(parsed_url.path)
        
        # 如果没有文件名，使用默认名称
        if not filename or filename == '/':
            filename = 'downloaded_file'
        
        return filename
    
    def download_repository(self, github_url: str, output_dir: str = None) -> bool:
        """
        下载整个仓库（作为 ZIP 文件）
        
        Args:
            github_url: GitHub 仓库 URL
            output_dir: 输出目录
            
        Returns:
            下载是否成功
        """
        parsed = self.parse_github_url(github_url)
        if not parsed:
            print("无法解析 GitHub URL")
            return False
        
        owner, repo, branch, _ = parsed
        
        # 构建 ZIP 下载链接
        zip_url = f"https://github.com/{owner}/{repo}/archive/refs/heads/{branch}.zip"
        
        # 确定输出路径
        if output_dir is None:
            output_dir = "."
        
        zip_filename = f"{repo}-{branch}.zip"
        output_path = os.path.join(output_dir, zip_filename)
        
        return self.download_file(zip_url, output_path)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='GitHub 文件加速下载器')
    parser.add_argument('url', help='GitHub 文件或仓库 URL')
    parser.add_argument('-o', '--output', help='输出路径')
    parser.add_argument('-d', '--dir', help='输出目录（用于仓库下载）')
    parser.add_argument('--no-mirror', action='store_true', help='不使用镜像站')
    parser.add_argument('--repo', action='store_true', help='下载整个仓库')
    parser.add_argument('--mirror-url', default='https://gh.imixc.top', 
                       help='镜像站地址（默认: https://gh.imixc.top）')
    
    args = parser.parse_args()
    
    # 创建下载器
    downloader = GitHubDownloader(mirror_url=args.mirror_url)
    
    # 执行下载
    if args.repo:
        success = downloader.download_repository(args.url, args.dir)
    else:
        success = downloader.download_file(args.url, args.output, use_mirror=not args.no_mirror)
    
    if success:
        print("下载成功！")
        sys.exit(0)
    else:
        print("下载失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
