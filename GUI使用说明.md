# GitHub 文件加速下载器 - GUI 使用说明

## 启动程序

### Windows 用户
1. **双击运行**: 直接双击 `启动下载器.bat` 文件
2. **命令行运行**: 打开命令提示符，运行 `python github_downloader_gui.py`

### Linux/Mac 用户
```bash
python3 github_downloader_gui.py
```

## 界面介绍

### 主窗口布局

```
┌─────────────────────────────────────────────────────────────┐
│                GitHub 文件加速下载器                        │
├─────────────────────────────────────────────────────────────┤
│ 下载设置                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ GitHub URL: [输入框________________________] [粘贴]    │ │
│ │ 下载类型:   ○ 单个文件  ○ 整个仓库(ZIP)              │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 输出设置                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 保存到:     [路径输入框___________________] [浏览]      │ │
│ │ 文件名:     [文件名输入框_________________]             │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 高级设置                                                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 镜像站:     [下拉选择框___________________]             │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│           [开始下载]  [清空]  [退出]                        │
├─────────────────────────────────────────────────────────────┤
│ 状态: 就绪                                                  │
└─────────────────────────────────────────────────────────────┘
```

## 使用步骤

### 1. 输入 GitHub URL
- 在 "GitHub URL" 输入框中粘贴或输入要下载的 GitHub 链接
- 点击 "粘贴" 按钮可以自动粘贴剪贴板中的链接
- 支持的 URL 格式：
  - 文件链接: `https://github.com/owner/repo/blob/branch/path/file.ext`
  - 仓库链接: `https://github.com/owner/repo`
  - Raw 链接: `https://github.com/owner/repo/raw/branch/path/file.ext`

### 2. 选择下载类型
- **单个文件**: 下载指定的单个文件
- **整个仓库(ZIP)**: 下载整个仓库的 ZIP 压缩包

### 3. 设置输出路径
- **保存到**: 选择文件保存的目录
- **文件名**: 自定义下载文件的名称（仅单个文件下载时可用）
- 点击 "浏览" 按钮可以图形化选择保存位置

### 4. 配置镜像站（可选）
- 从下拉菜单中选择镜像站：
  - `https://gh.imixc.top` (默认)
  - `https://github.com.cnpmjs.org`
  - `https://hub.fastgit.xyz`
  - `不使用镜像站` (直接从 GitHub 下载)

### 5. 开始下载
- 点击 "开始下载" 按钮启动下载
- 程序会自动打开进度窗口显示下载状态

## 进度窗口

下载开始后会弹出进度窗口，显示：

```
┌─────────────────────────────────────────────────────────────┐
│                        下载进度                             │
├─────────────────────────────────────────────────────────────┤
│ 下载链接: https://github.com/...                           │
│ 下载进度: [████████████████████████████] 100%              │
│ 进度: 100% (1024/1024 bytes) 速度: 2.5 MB/s               │
├─────────────────────────────────────────────────────────────┤
│ 下载日志:                                                   │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 14:30:25 - 开始下载: https://github.com/...           │ │
│ │ 14:30:26 - 正在下载: https://gh.imixc.top/...         │ │
│ │ 14:30:28 - ✅ 下载成功完成                             │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      [关闭]                                 │
└─────────────────────────────────────────────────────────────┘
```

## 功能特点

### 智能 URL 处理
- 自动识别 GitHub URL 类型
- 自动转换 blob 链接为 raw 链接
- 支持仓库根目录链接自动处理

### 镜像站加速
- 多个镜像站可选
- 自动回退机制：镜像站失败时自动使用原始 GitHub 链接
- 实时切换镜像站无需重启程序

### 用户友好界面
- 简洁直观的操作界面
- 实时进度显示
- 详细的下载日志
- 错误提示和处理建议

### 文件管理
- 自动创建不存在的目录
- 智能文件命名
- 支持自定义保存路径和文件名

## 常见问题

### Q: 程序无法启动
**A**: 检查 Python 环境和依赖包：
```bash
python --version  # 确保 Python 3.6+
pip install -r requirements.txt
```

### Q: 下载失败
**A**: 可能的原因和解决方案：
1. **网络问题**: 检查网络连接，尝试更换镜像站
2. **URL 错误**: 确认 GitHub URL 格式正确
3. **权限问题**: 确保对保存目录有写入权限

### Q: 镜像站无法访问
**A**: 程序会自动回退到原始 GitHub 链接，或手动选择其他镜像站

### Q: 下载速度慢
**A**: 尝试以下方法：
1. 更换不同的镜像站
2. 检查网络连接质量
3. 选择网络较好的时间段下载

## 快捷键

- `Ctrl+V`: 在 URL 输入框中粘贴链接
- `Enter`: 在 URL 输入框中按回车开始下载
- `Escape`: 关闭进度窗口（下载完成后）

## 技术支持

如遇到问题，请检查：
1. Python 版本是否为 3.6 或更高
2. 是否安装了所需依赖包
3. 网络连接是否正常
4. GitHub URL 格式是否正确

更多技术细节请参考 `README.md` 文档。
